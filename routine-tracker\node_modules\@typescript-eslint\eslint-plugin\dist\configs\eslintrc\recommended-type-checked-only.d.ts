declare const _default: {
    extends: string[];
    rules: {
        '@typescript-eslint/await-thenable': "error";
        '@typescript-eslint/no-array-delete': "error";
        '@typescript-eslint/no-base-to-string': "error";
        '@typescript-eslint/no-duplicate-type-constituents': "error";
        '@typescript-eslint/no-floating-promises': "error";
        '@typescript-eslint/no-for-in-array': "error";
        'no-implied-eval': "off";
        '@typescript-eslint/no-implied-eval': "error";
        '@typescript-eslint/no-misused-promises': "error";
        '@typescript-eslint/no-redundant-type-constituents': "error";
        '@typescript-eslint/no-unnecessary-type-assertion': "error";
        '@typescript-eslint/no-unsafe-argument': "error";
        '@typescript-eslint/no-unsafe-assignment': "error";
        '@typescript-eslint/no-unsafe-call': "error";
        '@typescript-eslint/no-unsafe-enum-comparison': "error";
        '@typescript-eslint/no-unsafe-member-access': "error";
        '@typescript-eslint/no-unsafe-return': "error";
        '@typescript-eslint/no-unsafe-unary-minus': "error";
        'no-throw-literal': "off";
        '@typescript-eslint/only-throw-error': "error";
        'prefer-promise-reject-errors': "off";
        '@typescript-eslint/prefer-promise-reject-errors': "error";
        'require-await': "off";
        '@typescript-eslint/require-await': "error";
        '@typescript-eslint/restrict-plus-operands': "error";
        '@typescript-eslint/restrict-template-expressions': "error";
        '@typescript-eslint/unbound-method': "error";
    };
};
export = _default;
//# sourceMappingURL=recommended-type-checked-only.d.ts.map