'use client';

import { useState } from 'react';

interface SignInProps {
  onSignIn: (name: string) => void;
}

export default function SignIn({ onSignIn }: SignInProps) {
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;
    
    setIsLoading(true);
    
    // Simulate a brief loading state for better UX
    await new Promise(resolve => setTimeout(resolve, 800));
    
    onSignIn(name.trim());
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      {/* Background Effects */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-yellow-200/30 to-orange-200/30 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-orange-200/30 to-yellow-300/30 rounded-full blur-3xl floating" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        {/* Welcome Header */}
        <div className="text-center mb-12">
          <div className="mb-8">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-3xl">✨</span>
            </div>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-yellow-600 via-orange-500 to-yellow-700 bg-clip-text text-transparent">
            Welcome
          </h1>
          <h2 className="text-2xl md:text-3xl font-semibold mb-6 text-gray-700">
            Digital Routine Tracker
          </h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            Track your daily growth with intention.<br />
            <span className="text-yellow-600 font-medium">No pressure, just accountability and reflection.</span>
          </p>
        </div>

        {/* Sign In Form */}
        <div className="card glass-effect shadow-xl border-2 border-yellow-200/50">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-lg font-semibold text-gray-700 mb-3">
                What's your name?
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your name here..."
                className="input-field text-lg bg-white/80 border-yellow-200 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-200"
                autoFocus
                disabled={isLoading}
                maxLength={50}
              />
            </div>

            <button
              type="submit"
              disabled={!name.trim() || isLoading}
              className={`w-full btn-primary text-lg py-4 font-bold transition-all duration-300 ${
                isLoading ? 'btn-loading' : ''
              } ${
                !name.trim() ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 hover:shadow-xl'
              }`}
            >
              {isLoading ? '' : 'Start Tracking 🚀'}
            </button>
          </form>

          {/* Features Preview */}
          <div className="mt-8 pt-6 border-t border-yellow-200/50">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl mb-1">📊</div>
                <div className="text-sm font-medium text-gray-700">Track Progress</div>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl mb-1">🔥</div>
                <div className="text-sm font-medium text-gray-700">Build Streaks</div>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl mb-1">📈</div>
                <div className="text-sm font-medium text-gray-700">View Analytics</div>
              </div>
              <div className="p-3 bg-yellow-50 rounded-lg">
                <div className="text-2xl mb-1">🎯</div>
                <div className="text-sm font-medium text-gray-700">Stay Consistent</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>✨ Your journey to better habits starts here</p>
        </div>
      </div>
    </div>
  );
}
