@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Golden Light Color Palette */
  --gold-50: #fffef7;
  --gold-100: #fffbeb;
  --gold-200: #fef3c7;
  --gold-300: #fde68a;
  --gold-400: #fcd34d;
  --gold-500: #f59e0b;
  --gold-600: #d97706;
  --gold-700: #b45309;
  --gold-800: #92400e;
  --gold-900: #78350f;

  /* Light Theme with Golden Accents */
  --bg-primary: linear-gradient(135deg, #fefce8 0%, #fef3c7 50%, #fde68a 100%);
  --bg-secondary: #ffffff;
  --bg-tertiary: #fefce8;
  --bg-card: rgba(255, 255, 255, 0.9);
  --bg-glass: rgba(255, 255, 255, 0.7);
  --bg-overlay: rgba(0, 0, 0, 0.1);

  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --text-gold: #d97706;
  --text-accent: #f59e0b;

  /* Border & Effects */
  --border-primary: rgba(217, 119, 6, 0.2);
  --border-secondary: rgba(0, 0, 0, 0.1);
  --border-light: rgba(255, 255, 255, 0.3);
  --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-gold: 0 8px 32px rgba(217, 119, 6, 0.15);
  --shadow-gold-intense: 0 12px 40px rgba(217, 119, 6, 0.25);

  /* Gradients */
  --gradient-gold: linear-gradient(135deg, #fcd34d 0%, #f59e0b 50%, #d97706 100%);
  --gradient-gold-light: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #fcd34d 100%);
  --gradient-bg: linear-gradient(135deg, #fefce8 0%, #fef3c7 30%, #fde68a 70%, #fcd34d 100%);
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 100%);
  --gradient-button: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-button-hover: linear-gradient(135deg, #d97706 0%, #b45309 100%);

  /* Legacy support */
  --background: #fefce8;
  --foreground: var(--text-primary);
}

@theme inline {
  --color-background: #fefce8;
  --color-foreground: #1f2937;
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--gradient-bg);
  background-attachment: fixed;
  color: var(--text-primary);
  min-height: 100vh;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-button-hover);
}

/* Modern Glass Effect */
.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-light);
  border-radius: 24px;
  box-shadow: var(--shadow-soft);
}

/* Golden Glow Animation */
@keyframes golden-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(252, 211, 77, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(252, 211, 77, 0.6);
  }
}

.golden-glow {
  animation: golden-pulse 3s ease-in-out infinite;
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(252, 211, 77, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Button Styles */
.btn-primary {
  background: var(--gradient-gold);
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(217, 119, 6, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.2);
}

.btn-secondary {
  background: white;
  color: var(--text-gold);
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 12px;
  border: 2px solid var(--text-gold);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(217, 119, 6, 0.1);
}

.btn-secondary:hover {
  background: var(--text-gold);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.2);
}

/* Card Styles */
.card {
  background: var(--gradient-card);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-gold);
  opacity: 0.5;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-gold);
  border-color: var(--text-gold);
}

/* Input Styles */
.input-field {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-size: 16px;
  transition: all 0.3s ease;
  width: 100%;
  font-family: inherit;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-field:focus {
  outline: none;
  border-color: var(--text-gold);
  box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  background: white;
}

.input-field:hover {
  border-color: #d1d5db;
}

.input-field::placeholder {
  color: var(--text-muted);
}

/* Checkbox Styles */
.checkbox-container {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(26, 26, 26, 0.4);
  border: 1px solid var(--border-secondary);
  margin-bottom: 8px;
}

.checkbox-container:hover {
  background: rgba(26, 26, 26, 0.6);
  border-color: var(--border-primary);
  transform: translateX(4px);
}

.checkbox-container.checked {
  background: rgba(252, 211, 77, 0.1);
  border-color: var(--text-gold);
  box-shadow: 0 0 10px rgba(252, 211, 77, 0.2);
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  height: 24px;
  width: 24px;
  background: transparent;
  border: 2px solid var(--border-secondary);
  border-radius: 6px;
  margin-right: 12px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox-container.checked .checkmark {
  background: var(--gradient-gold);
  border-color: var(--text-gold);
}

.checkmark::after {
  content: "";
  position: absolute;
  display: none;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid var(--bg-primary);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-container.checked .checkmark::after {
  display: block;
}

.checkbox-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

.checkbox-container.checked .checkbox-label {
  color: var(--text-gold);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(26, 26, 26, 0.6);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-gold);
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Mobile Responsive Enhancements */
@media (max-width: 640px) {
  .card {
    padding: 16px;
    border-radius: 16px;
  }

  .checkbox-container {
    padding: 12px;
    margin-bottom: 6px;
  }

  .checkbox-label {
    font-size: 14px;
  }

  .btn-primary, .btn-secondary {
    padding: 10px 20px;
    font-size: 14px;
  }

  .input-field {
    padding: 10px 14px;
    font-size: 14px;
  }
}

/* Micro-interactions */
.btn-primary, .btn-secondary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before, .btn-secondary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-primary:active::before, .btn-secondary:active::before {
  width: 300px;
  height: 300px;
}

/* Enhanced hover effects */
.checkbox-container:hover .checkmark {
  border-color: var(--border-primary);
  box-shadow: 0 0 10px rgba(252, 211, 77, 0.2);
}

.input-field:hover {
  border-color: rgba(252, 211, 77, 0.3);
}

/* Loading animation for buttons */
.btn-loading {
  position: relative;
  color: transparent;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus styles for accessibility */
.btn-primary:focus, .btn-secondary:focus, .input-field:focus {
  outline: 2px solid var(--text-gold);
  outline-offset: 2px;
}

.checkbox-container:focus-within {
  outline: 2px solid var(--text-gold);
  outline-offset: 2px;
  border-radius: 12px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: rgba(255, 215, 0, 0.8);
    --border-secondary: rgba(255, 255, 255, 0.3);
    --text-gold: #ffdd00;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating, .golden-glow, .shimmer, .progress-fill::after {
    animation: none;
  }

  * {
    transition: none;
  }
}
