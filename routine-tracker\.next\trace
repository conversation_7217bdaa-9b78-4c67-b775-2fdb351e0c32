[{"name": "hot-reloader", "duration": 598, "timestamp": 77193930543, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752061621661, "traceId": "dd9f4dcb0125dcfe"}, {"name": "setup-dev-bundler", "duration": 16106335, "timestamp": 77192350265, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752061620080, "traceId": "dd9f4dcb0125dcfe"}, {"name": "run-instrumentation-hook", "duration": 82, "timestamp": 77208772612, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752061636502, "traceId": "dd9f4dcb0125dcfe"}, {"name": "start-dev-server", "duration": 18977434, "timestamp": 77189871906, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "4236009472", "memory.totalMem": "12760211456", "memory.heapSizeLimit": "6429868032", "memory.rss": "179048448", "memory.heapTotal": "102154240", "memory.heapUsed": "69021112"}, "startTime": 1752061617602, "traceId": "dd9f4dcb0125dcfe"}, {"name": "compile-path", "duration": 41208139, "timestamp": 77208835298, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752061636565, "traceId": "dd9f4dcb0125dcfe"}, {"name": "ensure-page", "duration": 41216914, "timestamp": 77208834517, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752061636564, "traceId": "dd9f4dcb0125dcfe"}]