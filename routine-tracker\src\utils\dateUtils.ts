export const formatDate = (date: Date): string => {
  return date.toDateString();
};

export const isNewDay = (lastSavedDate: string): boolean => {
  const today = formatDate(new Date());
  return lastSavedDate !== today;
};

export const getTimeUntilMidnight = (): number => {
  const now = new Date();
  const midnight = new Date();
  midnight.setHours(24, 0, 0, 0);
  return midnight.getTime() - now.getTime();
};

export const formatTimeUntilMidnight = (): string => {
  const msUntilMidnight = getTimeUntilMidnight();
  const hours = Math.floor(msUntilMidnight / (1000 * 60 * 60));
  const minutes = Math.floor((msUntilMidnight % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m until reset`;
  }
  return `${minutes}m until reset`;
};

export const getDaysAgo = (dateString: string): number => {
  const date = new Date(dateString);
  const today = new Date();
  const diffTime = today.getTime() - date.getTime();
  return Math.floor(diffTime / (1000 * 60 * 60 * 24));
};

export const getWeeklyStats = (history: any[]): { week: number; completed: number; total: number }[] => {
  const now = new Date();
  const weeklyStats = [];
  
  for (let i = 0; i < 4; i++) {
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - (i * 7) - now.getDay());
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    const weekData = history.filter(entry => {
      const entryDate = new Date(entry.date);
      return entryDate >= weekStart && entryDate <= weekEnd;
    });
    
    const totalCompleted = weekData.reduce((sum, entry) => sum + entry.completedCount, 0);
    const totalPossible = weekData.reduce((sum, entry) => sum + entry.totalCount, 0);
    
    weeklyStats.push({
      week: i + 1,
      completed: totalCompleted,
      total: totalPossible
    });
  }
  
  return weeklyStats.reverse();
};
