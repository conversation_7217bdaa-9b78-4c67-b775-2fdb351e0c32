'use client';

import { useState } from 'react';
import { RoutineItem } from '@/utils/storage';

interface DashboardProps {
  userName: string;
  routines: RoutineItem[];
  onRoutineToggle: (id: string) => void;
  onSubmit: () => void;
  onUpdate: () => void;
  onShowHistory: () => void;
  onSignOut: () => void;
  isSubmitted: boolean;
  progress: number;
  currentDate: string;
  timeUntilReset: string;
  userStats: { currentStreak: number; totalDays: number };
}

export default function Dashboard({
  userName,
  routines,
  onRoutineToggle,
  onSubmit,
  onUpdate,
  onShowHistory,
  onSignOut,
  isSubmitted,
  progress,
  currentDate,
  timeUntilReset,
  userStats
}: DashboardProps) {
  const completedCount = routines.filter(r => r.completed).length;
  const totalCount = routines.length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-yellow-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-yellow-200/50 sticky top-0 z-40">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-md">
                <span className="text-xl">✨</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800">Welcome back, {userName}!</h1>
                <p className="text-sm text-gray-600">{currentDate}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {userStats.totalDays > 0 && (
                <button
                  onClick={onShowHistory}
                  className="btn-secondary px-4 py-2 text-sm flex items-center gap-2"
                >
                  📊 History
                </button>
              )}
              <button
                onClick={onSignOut}
                className="text-gray-500 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                title="Sign Out"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 py-8">
        {/* Stats Cards */}
        {userStats.totalDays > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-200/50 text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-1">{userStats.currentStreak}</div>
              <div className="text-sm text-gray-600">Current Streak</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-200/50 text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-1">{userStats.totalDays}</div>
              <div className="text-sm text-gray-600">Total Days</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-200/50 text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-1">{Math.round(progress)}%</div>
              <div className="text-sm text-gray-600">Today's Progress</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-200/50 text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-1">{completedCount}/{totalCount}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
        )}

        {/* Progress Section */}
        {isSubmitted && (
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-yellow-200/50 mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">Daily Progress</h2>
              <span className="text-2xl font-bold text-yellow-600">{Math.round(progress)}%</span>
            </div>
            <div className="progress-bar h-3 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="progress-fill h-full bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full transition-all duration-500" 
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {timeUntilReset && (
              <div className="mt-3 text-sm text-gray-500 text-center">
                ⏰ {timeUntilReset}
              </div>
            )}
          </div>
        )}

        {/* Routines Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-yellow-200/50 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800">
              {isSubmitted ? "Today's Achievements" : "Today's Goals"}
            </h2>
            {completedCount === totalCount && totalCount > 0 && (
              <span className="text-2xl">🎉</span>
            )}
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            {routines.map((routine) => (
              <label
                key={routine.id}
                className={`
                  flex items-center p-4 rounded-xl border-2 transition-all duration-200 cursor-pointer
                  ${routine.completed 
                    ? 'bg-yellow-50 border-yellow-300 shadow-sm' 
                    : 'bg-gray-50 border-gray-200 hover:border-yellow-200 hover:bg-yellow-50/50'
                  }
                  ${isSubmitted ? 'pointer-events-none opacity-75' : 'hover:scale-[1.02]'}
                `}
              >
                <input
                  type="checkbox"
                  checked={routine.completed}
                  onChange={() => onRoutineToggle(routine.id)}
                  disabled={isSubmitted}
                  className="sr-only"
                />
                <div className={`
                  w-6 h-6 rounded-lg border-2 flex items-center justify-center mr-4 transition-all
                  ${routine.completed 
                    ? 'bg-gradient-to-br from-yellow-400 to-orange-500 border-yellow-400' 
                    : 'border-gray-300 bg-white'
                  }
                `}>
                  {routine.completed && (
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <span className={`text-lg font-medium ${routine.completed ? 'text-gray-800' : 'text-gray-600'}`}>
                  {routine.name}
                </span>
              </label>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          {!isSubmitted ? (
            <button
              onClick={onSubmit}
              className="btn-primary px-12 py-4 text-lg font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              Submit Progress 🚀
            </button>
          ) : (
            <button
              onClick={onUpdate}
              className="btn-secondary px-12 py-4 text-lg font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              Update Progress ✏️
            </button>
          )}
        </div>

        {/* Motivational Footer */}
        <div className="text-center mt-12 text-gray-500">
          <p className="text-lg mb-2">
            {completedCount === totalCount && totalCount > 0
              ? "🎉 Perfect day! You're building amazing habits!"
              : "✨ Every small step counts towards your growth"
            }
          </p>
          <p className="text-sm">Your system. No judgment. Just growth.</p>
        </div>
      </main>
    </div>
  );
}
