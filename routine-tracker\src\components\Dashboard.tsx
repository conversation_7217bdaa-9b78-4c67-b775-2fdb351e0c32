'use client';

import { useState } from 'react';
import { RoutineItem } from '@/utils/storage';

interface DashboardProps {
  userName: string;
  routines: RoutineItem[];
  onRoutineToggle: (id: string) => void;
  onSubmit: () => void;
  onUpdate: () => void;
  onShowHistory: () => void;
  onSignOut: () => void;
  isSubmitted: boolean;
  progress: number;
  currentDate: string;
  timeUntilReset: string;
  userStats: { currentStreak: number; totalDays: number };
}

export default function Dashboard({
  userName,
  routines,
  onRoutineToggle,
  onSubmit,
  onUpdate,
  onShowHistory,
  onSignOut,
  isSubmitted,
  progress,
  currentDate,
  timeUntilReset,
  userStats
}: DashboardProps) {
  const completedCount = routines.filter(r => r.completed).length;
  const totalCount = routines.length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="bg-black/50 backdrop-blur-sm border-b border-yellow-400/30 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl lg:text-2xl font-bold text-white">Welcome back, {userName}!</h1>
                <p className="text-sm text-gray-400 flex items-center gap-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {currentDate}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {userStats.totalDays > 0 && (
                <button
                  onClick={onShowHistory}
                  className="btn-secondary px-4 py-2 text-sm flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  History
                </button>
              )}
              <button
                onClick={onSignOut}
                className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-800 transition-colors"
                title="Sign Out"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 lg:px-8 py-8">
        {/* Stats Cards */}
        {userStats.totalDays > 0 && (
          <div className="stats-grid mb-8">
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 text-center hover:border-yellow-400/50 transition-all">
              <svg className="w-8 h-8 text-yellow-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
              </svg>
              <div className="text-3xl font-bold text-yellow-400 mb-1">{userStats.currentStreak}</div>
              <div className="text-sm text-gray-400">Current Streak</div>
            </div>
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 text-center hover:border-yellow-400/50 transition-all">
              <svg className="w-8 h-8 text-yellow-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <div className="text-3xl font-bold text-yellow-400 mb-1">{userStats.totalDays}</div>
              <div className="text-sm text-gray-400">Total Days</div>
            </div>
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 text-center hover:border-yellow-400/50 transition-all">
              <svg className="w-8 h-8 text-yellow-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              <div className="text-3xl font-bold text-yellow-400 mb-1">{Math.round(progress)}%</div>
              <div className="text-sm text-gray-400">Today's Progress</div>
            </div>
            <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 border border-yellow-400/30 text-center hover:border-yellow-400/50 transition-all">
              <svg className="w-8 h-8 text-yellow-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="text-3xl font-bold text-yellow-400 mb-1">{completedCount}/{totalCount}</div>
              <div className="text-sm text-gray-400">Completed</div>
            </div>
          </div>
        )}

        {/* Progress Section */}
        {isSubmitted && (
          <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-yellow-400/30 mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl lg:text-2xl font-semibold text-white flex items-center gap-3">
                <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Daily Progress
              </h2>
              <span className="text-3xl font-bold text-yellow-400">{Math.round(progress)}%</span>
            </div>
            <div className="progress-bar h-4 bg-gray-700 rounded-full overflow-hidden">
              <div
                className="progress-fill h-full bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full transition-all duration-500"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {timeUntilReset && (
              <div className="mt-4 text-sm text-gray-400 text-center flex items-center justify-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {timeUntilReset}
              </div>
            )}
          </div>
        )}

        {/* Routines Section */}
        <div className="bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-yellow-400/30 mb-8">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-xl lg:text-2xl font-semibold text-white flex items-center gap-3">
              <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {isSubmitted ? "Today's Achievements" : "Today's Goals"}
            </h2>
            {completedCount === totalCount && totalCount > 0 && (
              <div className="flex items-center gap-2 text-yellow-400">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="font-semibold">Perfect Day!</span>
              </div>
            )}
          </div>

          <div className="routines-grid">
            {routines.map((routine) => {
              const getRoutineIcon = (name: string) => {
                if (name.includes('Prayer')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />;
                if (name.includes('Study')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />;
                if (name.includes('Hygiene')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />;
                if (name.includes('Work')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6" />;
                if (name.includes('Exercise')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />;
                if (name.includes('Reading')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />;
                if (name.includes('Meditation')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />;
                if (name.includes('Family')) return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />;
                return <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />;
              };

              return (
                <label
                  key={routine.id}
                  className={`
                    flex items-center p-6 rounded-xl border transition-all duration-200 cursor-pointer group
                    ${routine.completed
                      ? 'bg-gradient-to-br from-yellow-400/20 to-orange-500/20 border-yellow-400/50 shadow-lg'
                      : 'bg-gray-700/50 border-gray-600 hover:border-yellow-400/50 hover:bg-gray-700/70'
                    }
                    ${isSubmitted ? 'pointer-events-none opacity-75' : 'hover:scale-[1.02]'}
                  `}
                >
                  <input
                    type="checkbox"
                    checked={routine.completed}
                    onChange={() => onRoutineToggle(routine.id)}
                    disabled={isSubmitted}
                    className="sr-only"
                  />
                  <div className="flex items-center gap-4 w-full">
                    <svg className={`w-6 h-6 ${routine.completed ? 'text-yellow-400' : 'text-gray-400 group-hover:text-yellow-400'} transition-colors`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      {getRoutineIcon(routine.name)}
                    </svg>
                    <div className={`
                      w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all
                      ${routine.completed
                        ? 'bg-gradient-to-br from-yellow-400 to-orange-500 border-yellow-400'
                        : 'border-gray-500 bg-gray-800 group-hover:border-yellow-400'
                      }
                    `}>
                      {routine.completed && (
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </div>
                    <span className={`text-lg font-medium flex-1 ${routine.completed ? 'text-white' : 'text-gray-300 group-hover:text-white'} transition-colors`}>
                      {routine.name.replace(/^[^\s]+ /, '')}
                    </span>
                  </div>
                </label>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4 px-4 lg:px-0">
          {!isSubmitted ? (
            <button
              onClick={onSubmit}
              className="btn-primary px-8 lg:px-12 py-4 text-lg font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3 w-full max-w-xs lg:w-auto"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Submit Progress
            </button>
          ) : (
            <button
              onClick={onUpdate}
              className="btn-secondary px-8 lg:px-12 py-4 text-lg font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3 w-full max-w-xs lg:w-auto"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Update Progress
            </button>
          )}
        </div>

        {/* Motivational Footer */}
        <div className="text-center mt-12 text-gray-400">
          <p className="text-lg mb-2 flex items-center justify-center gap-2">
            {completedCount === totalCount && totalCount > 0 ? (
              <>
                <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-yellow-400">Perfect day! You're building amazing habits!</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span>Every small step counts towards your growth</span>
              </>
            )}
          </p>
          <p className="text-sm">Your system. No judgment. Just growth.</p>
        </div>
      </main>
    </div>
  );
}
