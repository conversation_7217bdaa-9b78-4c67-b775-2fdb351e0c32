export interface RoutineItem {
  id: string;
  name: string;
  completed: boolean;
}

export interface DailyProgress {
  date: string;
  userName: string;
  routines: RoutineItem[];
  completedCount: number;
  totalCount: number;
  submittedAt: string;
  lastUpdated: string;
}

export interface UserStats {
  totalDays: number;
  currentStreak: number;
  longestStreak: number;
  averageCompletion: number;
  favoriteRoutines: string[];
}

const STORAGE_KEYS = {
  DAILY_PROGRESS: (date: string) => `routine-${date}`,
  HISTORY: 'routine-history',
  USER_STATS: 'routine-user-stats',
  LAST_RESET: 'routine-last-reset'
};

export const saveDailyProgress = (progress: DailyProgress): void => {
  try {
    // Save today's progress
    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS(progress.date), JSON.stringify(progress));
    
    // Update history
    const history = getHistory();
    const existingIndex = history.findIndex(h => h.date === progress.date);
    
    if (existingIndex >= 0) {
      history[existingIndex] = progress;
    } else {
      history.push(progress);
    }
    
    // Keep only last 365 days
    const sortedHistory = history
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 365);
    
    localStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(sortedHistory));
    
    // Update user stats
    updateUserStats(sortedHistory);
  } catch (error) {
    console.error('Error saving daily progress:', error);
  }
};

export const getDailyProgress = (date: string): DailyProgress | null => {
  try {
    const data = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS(date));
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting daily progress:', error);
    return null;
  }
};

export const getHistory = (): DailyProgress[] => {
  try {
    const data = localStorage.getItem(STORAGE_KEYS.HISTORY);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error getting history:', error);
    return [];
  }
};

export const getUserStats = (): UserStats => {
  try {
    const data = localStorage.getItem(STORAGE_KEYS.USER_STATS);
    return data ? JSON.parse(data) : {
      totalDays: 0,
      currentStreak: 0,
      longestStreak: 0,
      averageCompletion: 0,
      favoriteRoutines: []
    };
  } catch (error) {
    console.error('Error getting user stats:', error);
    return {
      totalDays: 0,
      currentStreak: 0,
      longestStreak: 0,
      averageCompletion: 0,
      favoriteRoutines: []
    };
  }
};

const updateUserStats = (history: DailyProgress[]): void => {
  try {
    const stats: UserStats = {
      totalDays: history.length,
      currentStreak: calculateCurrentStreak(history),
      longestStreak: calculateLongestStreak(history),
      averageCompletion: calculateAverageCompletion(history),
      favoriteRoutines: calculateFavoriteRoutines(history)
    };
    
    localStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(stats));
  } catch (error) {
    console.error('Error updating user stats:', error);
  }
};

const calculateCurrentStreak = (history: DailyProgress[]): number => {
  if (history.length === 0) return 0;
  
  const sortedHistory = history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  let streak = 0;
  const today = new Date();
  
  for (let i = 0; i < sortedHistory.length; i++) {
    const entryDate = new Date(sortedHistory[i].date);
    const daysDiff = Math.floor((today.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === i && sortedHistory[i].completedCount > 0) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
};

const calculateLongestStreak = (history: DailyProgress[]): number => {
  if (history.length === 0) return 0;
  
  const sortedHistory = history.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  let longestStreak = 0;
  let currentStreak = 0;
  
  for (let i = 0; i < sortedHistory.length; i++) {
    if (sortedHistory[i].completedCount > 0) {
      currentStreak++;
      longestStreak = Math.max(longestStreak, currentStreak);
    } else {
      currentStreak = 0;
    }
  }
  
  return longestStreak;
};

const calculateAverageCompletion = (history: DailyProgress[]): number => {
  if (history.length === 0) return 0;
  
  const totalCompletion = history.reduce((sum, entry) => {
    return sum + (entry.completedCount / entry.totalCount) * 100;
  }, 0);
  
  return Math.round(totalCompletion / history.length);
};

const calculateFavoriteRoutines = (history: DailyProgress[]): string[] => {
  if (history.length === 0) return [];
  
  const routineCounts: { [key: string]: number } = {};
  
  history.forEach(entry => {
    entry.routines.forEach(routine => {
      if (routine.completed) {
        routineCounts[routine.name] = (routineCounts[routine.name] || 0) + 1;
      }
    });
  });
  
  return Object.entries(routineCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([name]) => name);
};

export const checkForMidnightReset = (): boolean => {
  try {
    const lastReset = localStorage.getItem(STORAGE_KEYS.LAST_RESET);
    const today = new Date().toDateString();
    
    if (!lastReset || lastReset !== today) {
      localStorage.setItem(STORAGE_KEYS.LAST_RESET, today);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking midnight reset:', error);
    return false;
  }
};

export const clearTodayProgress = (): void => {
  try {
    const today = new Date().toDateString();
    localStorage.removeItem(STORAGE_KEYS.DAILY_PROGRESS(today));
  } catch (error) {
    console.error('Error clearing today progress:', error);
  }
};
