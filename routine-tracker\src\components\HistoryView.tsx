'use client';

import { useState, useEffect } from 'react';
import { DailyProgress, getHistory, getUserStats, UserStats } from '@/utils/storage';
import { getDaysAgo, getWeeklyStats } from '@/utils/dateUtils';

interface HistoryViewProps {
  onClose: () => void;
}

export default function HistoryView({ onClose }: HistoryViewProps) {
  const [history, setHistory] = useState<DailyProgress[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    totalDays: 0,
    currentStreak: 0,
    longestStreak: 0,
    averageCompletion: 0,
    favoriteRoutines: []
  });
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');

  useEffect(() => {
    const historyData = getHistory();
    const stats = getUserStats();
    setHistory(historyData);
    setUserStats(stats);
  }, []);

  const getFilteredHistory = () => {
    const now = new Date();
    let cutoffDate = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case 'all':
        return history;
    }
    
    return history.filter(entry => new Date(entry.date) >= cutoffDate);
  };

  const filteredHistory = getFilteredHistory();
  const weeklyStats = getWeeklyStats(history);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="card glass-effect max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            Your Growth Journey 📊
          </h2>
          <button
            onClick={onClose}
            className="btn-secondary px-4 py-2 text-sm"
          >
            ✕ Close
          </button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20">
            <div className="text-2xl font-bold text-yellow-400">{userStats.totalDays}</div>
            <div className="text-sm text-gray-400">Total Days</div>
          </div>
          <div className="text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20">
            <div className="text-2xl font-bold text-yellow-400">{userStats.currentStreak}</div>
            <div className="text-sm text-gray-400">Current Streak</div>
          </div>
          <div className="text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20">
            <div className="text-2xl font-bold text-yellow-400">{userStats.longestStreak}</div>
            <div className="text-sm text-gray-400">Longest Streak</div>
          </div>
          <div className="text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20">
            <div className="text-2xl font-bold text-yellow-400">{userStats.averageCompletion}%</div>
            <div className="text-sm text-gray-400">Avg Completion</div>
          </div>
        </div>

        {/* Period Filter */}
        <div className="flex justify-center gap-2 mb-8">
          {(['week', 'month', 'all'] as const).map((period) => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedPeriod === period
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {period === 'week' ? 'Last 7 Days' : period === 'month' ? 'Last 30 Days' : 'All Time'}
            </button>
          ))}
        </div>

        {/* Weekly Progress Chart */}
        {selectedPeriod === 'week' && weeklyStats.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-white mb-4">Weekly Progress</h3>
            <div className="grid grid-cols-4 gap-4">
              {weeklyStats.map((week, index) => {
                const percentage = week.total > 0 ? (week.completed / week.total) * 100 : 0;
                return (
                  <div key={index} className="text-center">
                    <div className="h-32 bg-gray-800 rounded-lg mb-2 relative overflow-hidden">
                      <div 
                        className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-yellow-400 to-orange-500 transition-all duration-500"
                        style={{ height: `${percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-sm text-gray-400">Week {week.week}</div>
                    <div className="text-xs text-yellow-400">{Math.round(percentage)}%</div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Favorite Routines */}
        {userStats.favoriteRoutines.length > 0 && (
          <div className="mb-8">
            <h3 className="text-xl font-semibold text-white mb-4">Your Top Routines 🌟</h3>
            <div className="flex flex-wrap gap-2">
              {userStats.favoriteRoutines.map((routine, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full text-sm text-yellow-400 border border-yellow-400/30"
                >
                  {routine}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* History List */}
        <div>
          <h3 className="text-xl font-semibold text-white mb-4">Recent Activity</h3>
          {filteredHistory.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <div className="text-4xl mb-4">📝</div>
              <p>No activity yet. Start tracking your routines!</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredHistory
                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                .map((entry, index) => {
                  const daysAgo = getDaysAgo(entry.date);
                  const percentage = (entry.completedCount / entry.totalCount) * 100;
                  
                  return (
                    <div
                      key={index}
                      className="p-4 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-yellow-400/30 transition-all"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <div className="font-medium text-white">{entry.userName}</div>
                          <div className="text-sm text-gray-400">
                            {daysAgo === 0 ? 'Today' : daysAgo === 1 ? 'Yesterday' : `${daysAgo} days ago`}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-yellow-400">{Math.round(percentage)}%</div>
                          <div className="text-sm text-gray-400">{entry.completedCount}/{entry.totalCount}</div>
                        </div>
                      </div>
                      
                      <div className="progress-bar mb-3">
                        <div 
                          className="progress-fill" 
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {entry.routines.map((routine, routineIndex) => (
                          <span
                            key={routineIndex}
                            className={`text-xs px-2 py-1 rounded ${
                              routine.completed
                                ? 'bg-yellow-400/20 text-yellow-400'
                                : 'bg-gray-700 text-gray-400'
                            }`}
                          >
                            {routine.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  );
                })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
