export type Options = [
    {
        ignoredTypeNames?: string[];
        checkUnknown?: boolean;
    }
];
export type MessageIds = 'baseArrayJoin' | 'baseToString';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-base-to-string.d.ts.map