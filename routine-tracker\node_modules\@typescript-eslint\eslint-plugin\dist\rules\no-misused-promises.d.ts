import type { TSESLint } from '@typescript-eslint/utils';
export type Options = [
    {
        checksConditionals?: boolean;
        checksSpreads?: boolean;
        checksVoidReturn?: boolean | ChecksVoidReturnOptions;
    }
];
export interface ChecksVoidReturnOptions {
    arguments?: boolean;
    attributes?: boolean;
    inheritedMethods?: boolean;
    properties?: boolean;
    returns?: boolean;
    variables?: boolean;
}
export type MessageId = 'conditional' | 'predicate' | 'spread' | 'voidReturnArgument' | 'voidReturnAttribute' | 'voidReturnInheritedMethod' | 'voidReturnProperty' | 'voidReturnReturnValue' | 'voidReturnVariable';
declare const _default: TSESLint.RuleModule<MessageId, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-misused-promises.d.ts.map