{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/src/utils/storage.ts"], "sourcesContent": ["export interface RoutineItem {\n  id: string;\n  name: string;\n  completed: boolean;\n}\n\nexport interface DailyProgress {\n  date: string;\n  userName: string;\n  routines: RoutineItem[];\n  completedCount: number;\n  totalCount: number;\n  submittedAt: string;\n  lastUpdated: string;\n}\n\nexport interface UserStats {\n  totalDays: number;\n  currentStreak: number;\n  longestStreak: number;\n  averageCompletion: number;\n  favoriteRoutines: string[];\n}\n\nconst STORAGE_KEYS = {\n  DAILY_PROGRESS: (date: string) => `routine-${date}`,\n  HISTORY: 'routine-history',\n  USER_STATS: 'routine-user-stats',\n  LAST_RESET: 'routine-last-reset'\n};\n\nexport const saveDailyProgress = (progress: DailyProgress): void => {\n  try {\n    // Save today's progress\n    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS(progress.date), JSON.stringify(progress));\n    \n    // Update history\n    const history = getHistory();\n    const existingIndex = history.findIndex(h => h.date === progress.date);\n    \n    if (existingIndex >= 0) {\n      history[existingIndex] = progress;\n    } else {\n      history.push(progress);\n    }\n    \n    // Keep only last 365 days\n    const sortedHistory = history\n      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n      .slice(0, 365);\n    \n    localStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(sortedHistory));\n    \n    // Update user stats\n    updateUserStats(sortedHistory);\n  } catch (error) {\n    console.error('Error saving daily progress:', error);\n  }\n};\n\nexport const getDailyProgress = (date: string): DailyProgress | null => {\n  try {\n    const data = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS(date));\n    return data ? JSON.parse(data) : null;\n  } catch (error) {\n    console.error('Error getting daily progress:', error);\n    return null;\n  }\n};\n\nexport const getHistory = (): DailyProgress[] => {\n  try {\n    const data = localStorage.getItem(STORAGE_KEYS.HISTORY);\n    return data ? JSON.parse(data) : [];\n  } catch (error) {\n    console.error('Error getting history:', error);\n    return [];\n  }\n};\n\nexport const getUserStats = (): UserStats => {\n  try {\n    const data = localStorage.getItem(STORAGE_KEYS.USER_STATS);\n    return data ? JSON.parse(data) : {\n      totalDays: 0,\n      currentStreak: 0,\n      longestStreak: 0,\n      averageCompletion: 0,\n      favoriteRoutines: []\n    };\n  } catch (error) {\n    console.error('Error getting user stats:', error);\n    return {\n      totalDays: 0,\n      currentStreak: 0,\n      longestStreak: 0,\n      averageCompletion: 0,\n      favoriteRoutines: []\n    };\n  }\n};\n\nconst updateUserStats = (history: DailyProgress[]): void => {\n  try {\n    const stats: UserStats = {\n      totalDays: history.length,\n      currentStreak: calculateCurrentStreak(history),\n      longestStreak: calculateLongestStreak(history),\n      averageCompletion: calculateAverageCompletion(history),\n      favoriteRoutines: calculateFavoriteRoutines(history)\n    };\n    \n    localStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(stats));\n  } catch (error) {\n    console.error('Error updating user stats:', error);\n  }\n};\n\nconst calculateCurrentStreak = (history: DailyProgress[]): number => {\n  if (history.length === 0) return 0;\n  \n  const sortedHistory = history.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());\n  let streak = 0;\n  const today = new Date();\n  \n  for (let i = 0; i < sortedHistory.length; i++) {\n    const entryDate = new Date(sortedHistory[i].date);\n    const daysDiff = Math.floor((today.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));\n    \n    if (daysDiff === i && sortedHistory[i].completedCount > 0) {\n      streak++;\n    } else {\n      break;\n    }\n  }\n  \n  return streak;\n};\n\nconst calculateLongestStreak = (history: DailyProgress[]): number => {\n  if (history.length === 0) return 0;\n  \n  const sortedHistory = history.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());\n  let longestStreak = 0;\n  let currentStreak = 0;\n  \n  for (let i = 0; i < sortedHistory.length; i++) {\n    if (sortedHistory[i].completedCount > 0) {\n      currentStreak++;\n      longestStreak = Math.max(longestStreak, currentStreak);\n    } else {\n      currentStreak = 0;\n    }\n  }\n  \n  return longestStreak;\n};\n\nconst calculateAverageCompletion = (history: DailyProgress[]): number => {\n  if (history.length === 0) return 0;\n  \n  const totalCompletion = history.reduce((sum, entry) => {\n    return sum + (entry.completedCount / entry.totalCount) * 100;\n  }, 0);\n  \n  return Math.round(totalCompletion / history.length);\n};\n\nconst calculateFavoriteRoutines = (history: DailyProgress[]): string[] => {\n  if (history.length === 0) return [];\n  \n  const routineCounts: { [key: string]: number } = {};\n  \n  history.forEach(entry => {\n    entry.routines.forEach(routine => {\n      if (routine.completed) {\n        routineCounts[routine.name] = (routineCounts[routine.name] || 0) + 1;\n      }\n    });\n  });\n  \n  return Object.entries(routineCounts)\n    .sort(([, a], [, b]) => b - a)\n    .slice(0, 3)\n    .map(([name]) => name);\n};\n\nexport const checkForMidnightReset = (): boolean => {\n  try {\n    const lastReset = localStorage.getItem(STORAGE_KEYS.LAST_RESET);\n    const today = new Date().toDateString();\n    \n    if (!lastReset || lastReset !== today) {\n      localStorage.setItem(STORAGE_KEYS.LAST_RESET, today);\n      return true;\n    }\n    \n    return false;\n  } catch (error) {\n    console.error('Error checking midnight reset:', error);\n    return false;\n  }\n};\n\nexport const clearTodayProgress = (): void => {\n  try {\n    const today = new Date().toDateString();\n    localStorage.removeItem(STORAGE_KEYS.DAILY_PROGRESS(today));\n  } catch (error) {\n    console.error('Error clearing today progress:', error);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;AAwBA,MAAM,eAAe;IACnB,gBAAgB,CAAC,OAAiB,CAAC,QAAQ,EAAE,MAAM;IACnD,SAAS;IACT,YAAY;IACZ,YAAY;AACd;AAEO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,wBAAwB;QACxB,aAAa,OAAO,CAAC,aAAa,cAAc,CAAC,SAAS,IAAI,GAAG,KAAK,SAAS,CAAC;QAEhF,iBAAiB;QACjB,MAAM,UAAU;QAChB,MAAM,gBAAgB,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,SAAS,IAAI;QAErE,IAAI,iBAAiB,GAAG;YACtB,OAAO,CAAC,cAAc,GAAG;QAC3B,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;QAEA,0BAA0B;QAC1B,MAAM,gBAAgB,QACnB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,KAAK,CAAC,GAAG;QAEZ,aAAa,OAAO,CAAC,aAAa,OAAO,EAAE,KAAK,SAAS,CAAC;QAE1D,oBAAoB;QACpB,gBAAgB;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI;QACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,cAAc,CAAC;QAC9D,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEO,MAAM,aAAa;IACxB,IAAI;QACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,OAAO;QACtD,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,EAAE;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,EAAE;IACX;AACF;AAEO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,OAAO,aAAa,OAAO,CAAC,aAAa,UAAU;QACzD,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;YAC/B,WAAW;YACX,eAAe;YACf,eAAe;YACf,mBAAmB;YACnB,kBAAkB,EAAE;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,WAAW;YACX,eAAe;YACf,eAAe;YACf,mBAAmB;YACnB,kBAAkB,EAAE;QACtB;IACF;AACF;AAEA,MAAM,kBAAkB,CAAC;IACvB,IAAI;QACF,MAAM,QAAmB;YACvB,WAAW,QAAQ,MAAM;YACzB,eAAe,uBAAuB;YACtC,eAAe,uBAAuB;YACtC,mBAAmB,2BAA2B;YAC9C,kBAAkB,0BAA0B;QAC9C;QAEA,aAAa,OAAO,CAAC,aAAa,UAAU,EAAE,KAAK,SAAS,CAAC;IAC/D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAEA,MAAM,yBAAyB,CAAC;IAC9B,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IAEjC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAClG,IAAI,SAAS;IACb,MAAM,QAAQ,IAAI;IAElB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC7C,MAAM,YAAY,IAAI,KAAK,aAAa,CAAC,EAAE,CAAC,IAAI;QAChD,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,MAAM,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1F,IAAI,aAAa,KAAK,aAAa,CAAC,EAAE,CAAC,cAAc,GAAG,GAAG;YACzD;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT;AAEA,MAAM,yBAAyB,CAAC;IAC9B,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IAEjC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAClG,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC7C,IAAI,aAAa,CAAC,EAAE,CAAC,cAAc,GAAG,GAAG;YACvC;YACA,gBAAgB,KAAK,GAAG,CAAC,eAAe;QAC1C,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,OAAO;AACT;AAEA,MAAM,6BAA6B,CAAC;IAClC,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IAEjC,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC3C,OAAO,MAAM,AAAC,MAAM,cAAc,GAAG,MAAM,UAAU,GAAI;IAC3D,GAAG;IAEH,OAAO,KAAK,KAAK,CAAC,kBAAkB,QAAQ,MAAM;AACpD;AAEA,MAAM,4BAA4B,CAAC;IACjC,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO,EAAE;IAEnC,MAAM,gBAA2C,CAAC;IAElD,QAAQ,OAAO,CAAC,CAAA;QACd,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI,QAAQ,SAAS,EAAE;gBACrB,aAAa,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI;YACrE;QACF;IACF;IAEA,OAAO,OAAO,OAAO,CAAC,eACnB,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,GAAK,IAAI,GAC3B,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;AACrB;AAEO,MAAM,wBAAwB;IACnC,IAAI;QACF,MAAM,YAAY,aAAa,OAAO,CAAC,aAAa,UAAU;QAC9D,MAAM,QAAQ,IAAI,OAAO,YAAY;QAErC,IAAI,CAAC,aAAa,cAAc,OAAO;YACrC,aAAa,OAAO,CAAC,aAAa,UAAU,EAAE;YAC9C,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,aAAa,UAAU,CAAC,aAAa,cAAc,CAAC;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/src/utils/dateUtils.ts"], "sourcesContent": ["export const formatDate = (date: Date): string => {\n  return date.toDateString();\n};\n\nexport const isNewDay = (lastSavedDate: string): boolean => {\n  const today = formatDate(new Date());\n  return lastSavedDate !== today;\n};\n\nexport const getTimeUntilMidnight = (): number => {\n  const now = new Date();\n  const midnight = new Date();\n  midnight.setHours(24, 0, 0, 0);\n  return midnight.getTime() - now.getTime();\n};\n\nexport const formatTimeUntilMidnight = (): string => {\n  const msUntilMidnight = getTimeUntilMidnight();\n  const hours = Math.floor(msUntilMidnight / (1000 * 60 * 60));\n  const minutes = Math.floor((msUntilMidnight % (1000 * 60 * 60)) / (1000 * 60));\n  \n  if (hours > 0) {\n    return `${hours}h ${minutes}m until reset`;\n  }\n  return `${minutes}m until reset`;\n};\n\nexport const getDaysAgo = (dateString: string): number => {\n  const date = new Date(dateString);\n  const today = new Date();\n  const diffTime = today.getTime() - date.getTime();\n  return Math.floor(diffTime / (1000 * 60 * 60 * 24));\n};\n\nexport const getWeeklyStats = (history: any[]): { week: number; completed: number; total: number }[] => {\n  const now = new Date();\n  const weeklyStats = [];\n  \n  for (let i = 0; i < 4; i++) {\n    const weekStart = new Date(now);\n    weekStart.setDate(now.getDate() - (i * 7) - now.getDay());\n    weekStart.setHours(0, 0, 0, 0);\n    \n    const weekEnd = new Date(weekStart);\n    weekEnd.setDate(weekStart.getDate() + 6);\n    weekEnd.setHours(23, 59, 59, 999);\n    \n    const weekData = history.filter(entry => {\n      const entryDate = new Date(entry.date);\n      return entryDate >= weekStart && entryDate <= weekEnd;\n    });\n    \n    const totalCompleted = weekData.reduce((sum, entry) => sum + entry.completedCount, 0);\n    const totalPossible = weekData.reduce((sum, entry) => sum + entry.totalCount, 0);\n    \n    weeklyStats.push({\n      week: i + 1,\n      completed: totalCompleted,\n      total: totalPossible\n    });\n  }\n  \n  return weeklyStats.reverse();\n};\n"], "names": [], "mappings": ";;;;;;;;AAAO,MAAM,aAAa,CAAC;IACzB,OAAO,KAAK,YAAY;AAC1B;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ,WAAW,IAAI;IAC7B,OAAO,kBAAkB;AAC3B;AAEO,MAAM,uBAAuB;IAClC,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI;IACrB,SAAS,QAAQ,CAAC,IAAI,GAAG,GAAG;IAC5B,OAAO,SAAS,OAAO,KAAK,IAAI,OAAO;AACzC;AAEO,MAAM,0BAA0B;IACrC,MAAM,kBAAkB;IACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,kBAAkB,CAAC,OAAO,KAAK,EAAE;IAC1D,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,kBAAkB,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IAE5E,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,aAAa,CAAC;IAC5C;IACA,OAAO,GAAG,QAAQ,aAAa,CAAC;AAClC;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,IAAI;IAClB,MAAM,WAAW,MAAM,OAAO,KAAK,KAAK,OAAO;IAC/C,OAAO,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AACnD;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,EAAE;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,MAAM,YAAY,IAAI,KAAK;QAC3B,UAAU,OAAO,CAAC,IAAI,OAAO,KAAM,IAAI,IAAK,IAAI,MAAM;QACtD,UAAU,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE5B,MAAM,UAAU,IAAI,KAAK;QACzB,QAAQ,OAAO,CAAC,UAAU,OAAO,KAAK;QACtC,QAAQ,QAAQ,CAAC,IAAI,IAAI,IAAI;QAE7B,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAA;YAC9B,MAAM,YAAY,IAAI,KAAK,MAAM,IAAI;YACrC,OAAO,aAAa,aAAa,aAAa;QAChD;QAEA,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,cAAc,EAAE;QACnF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,EAAE;QAE9E,YAAY,IAAI,CAAC;YACf,MAAM,IAAI;YACV,WAAW;YACX,OAAO;QACT;IACF;IAEA,OAAO,YAAY,OAAO;AAC5B", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/src/components/HistoryView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { DailyProgress, getHistory, getUserStats, UserStats } from '@/utils/storage';\nimport { getDaysAgo, getWeeklyStats } from '@/utils/dateUtils';\n\ninterface HistoryViewProps {\n  onClose: () => void;\n}\n\nexport default function HistoryView({ onClose }: HistoryViewProps) {\n  const [history, setHistory] = useState<DailyProgress[]>([]);\n  const [userStats, setUserStats] = useState<UserStats>({\n    totalDays: 0,\n    currentStreak: 0,\n    longestStreak: 0,\n    averageCompletion: 0,\n    favoriteRoutines: []\n  });\n  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');\n\n  useEffect(() => {\n    const historyData = getHistory();\n    const stats = getUserStats();\n    setHistory(historyData);\n    setUserStats(stats);\n  }, []);\n\n  const getFilteredHistory = () => {\n    const now = new Date();\n    let cutoffDate = new Date();\n    \n    switch (selectedPeriod) {\n      case 'week':\n        cutoffDate.setDate(now.getDate() - 7);\n        break;\n      case 'month':\n        cutoffDate.setDate(now.getDate() - 30);\n        break;\n      case 'all':\n        return history;\n    }\n    \n    return history.filter(entry => new Date(entry.date) >= cutoffDate);\n  };\n\n  const filteredHistory = getFilteredHistory();\n  const weeklyStats = getWeeklyStats(history);\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n      <div className=\"card glass-effect max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center mb-8\">\n          <h2 className=\"text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent\">\n            Your Growth Journey 📊\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"btn-secondary px-4 py-2 text-sm\"\n          >\n            ✕ Close\n          </button>\n        </div>\n\n        {/* Stats Overview */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8\">\n          <div className=\"text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20\">\n            <div className=\"text-2xl font-bold text-yellow-400\">{userStats.totalDays}</div>\n            <div className=\"text-sm text-gray-400\">Total Days</div>\n          </div>\n          <div className=\"text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20\">\n            <div className=\"text-2xl font-bold text-yellow-400\">{userStats.currentStreak}</div>\n            <div className=\"text-sm text-gray-400\">Current Streak</div>\n          </div>\n          <div className=\"text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20\">\n            <div className=\"text-2xl font-bold text-yellow-400\">{userStats.longestStreak}</div>\n            <div className=\"text-sm text-gray-400\">Longest Streak</div>\n          </div>\n          <div className=\"text-center p-4 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-lg border border-yellow-400/20\">\n            <div className=\"text-2xl font-bold text-yellow-400\">{userStats.averageCompletion}%</div>\n            <div className=\"text-sm text-gray-400\">Avg Completion</div>\n          </div>\n        </div>\n\n        {/* Period Filter */}\n        <div className=\"flex justify-center gap-2 mb-8\">\n          {(['week', 'month', 'all'] as const).map((period) => (\n            <button\n              key={period}\n              onClick={() => setSelectedPeriod(period)}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${\n                selectedPeriod === period\n                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black'\n                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'\n              }`}\n            >\n              {period === 'week' ? 'Last 7 Days' : period === 'month' ? 'Last 30 Days' : 'All Time'}\n            </button>\n          ))}\n        </div>\n\n        {/* Weekly Progress Chart */}\n        {selectedPeriod === 'week' && weeklyStats.length > 0 && (\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-semibold text-white mb-4\">Weekly Progress</h3>\n            <div className=\"grid grid-cols-4 gap-4\">\n              {weeklyStats.map((week, index) => {\n                const percentage = week.total > 0 ? (week.completed / week.total) * 100 : 0;\n                return (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"h-32 bg-gray-800 rounded-lg mb-2 relative overflow-hidden\">\n                      <div \n                        className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-yellow-400 to-orange-500 transition-all duration-500\"\n                        style={{ height: `${percentage}%` }}\n                      ></div>\n                    </div>\n                    <div className=\"text-sm text-gray-400\">Week {week.week}</div>\n                    <div className=\"text-xs text-yellow-400\">{Math.round(percentage)}%</div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {/* Favorite Routines */}\n        {userStats.favoriteRoutines.length > 0 && (\n          <div className=\"mb-8\">\n            <h3 className=\"text-xl font-semibold text-white mb-4\">Your Top Routines 🌟</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {userStats.favoriteRoutines.map((routine, index) => (\n                <span\n                  key={index}\n                  className=\"px-3 py-1 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full text-sm text-yellow-400 border border-yellow-400/30\"\n                >\n                  {routine}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* History List */}\n        <div>\n          <h3 className=\"text-xl font-semibold text-white mb-4\">Recent Activity</h3>\n          {filteredHistory.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-400\">\n              <div className=\"text-4xl mb-4\">📝</div>\n              <p>No activity yet. Start tracking your routines!</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n              {filteredHistory\n                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n                .map((entry, index) => {\n                  const daysAgo = getDaysAgo(entry.date);\n                  const percentage = (entry.completedCount / entry.totalCount) * 100;\n                  \n                  return (\n                    <div\n                      key={index}\n                      className=\"p-4 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-yellow-400/30 transition-all\"\n                    >\n                      <div className=\"flex justify-between items-start mb-2\">\n                        <div>\n                          <div className=\"font-medium text-white\">{entry.userName}</div>\n                          <div className=\"text-sm text-gray-400\">\n                            {daysAgo === 0 ? 'Today' : daysAgo === 1 ? 'Yesterday' : `${daysAgo} days ago`}\n                          </div>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-lg font-bold text-yellow-400\">{Math.round(percentage)}%</div>\n                          <div className=\"text-sm text-gray-400\">{entry.completedCount}/{entry.totalCount}</div>\n                        </div>\n                      </div>\n                      \n                      <div className=\"progress-bar mb-3\">\n                        <div \n                          className=\"progress-fill\" \n                          style={{ width: `${percentage}%` }}\n                        ></div>\n                      </div>\n                      \n                      <div className=\"flex flex-wrap gap-1\">\n                        {entry.routines.map((routine, routineIndex) => (\n                          <span\n                            key={routineIndex}\n                            className={`text-xs px-2 py-1 rounded ${\n                              routine.completed\n                                ? 'bg-yellow-400/20 text-yellow-400'\n                                : 'bg-gray-700 text-gray-400'\n                            }`}\n                          >\n                            {routine.name}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  );\n                })}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,YAAY,EAAE,OAAO,EAAoB;;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,WAAW;QACX,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,kBAAkB,EAAE;IACtB;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAE/E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;YAC7B,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;YACzB,WAAW;YACX,aAAa;QACf;gCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,MAAM,MAAM,IAAI;QAChB,IAAI,aAAa,IAAI;QAErB,OAAQ;YACN,KAAK;gBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;gBACnC;YACF,KAAK;gBACH,WAAW,OAAO,CAAC,IAAI,OAAO,KAAK;gBACnC;YACF,KAAK;gBACH,OAAO;QACX;QAEA,OAAO,QAAQ,MAAM,CAAC,CAAA,QAAS,IAAI,KAAK,MAAM,IAAI,KAAK;IACzD;IAEA,MAAM,kBAAkB;IACxB,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE;IAEnC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAkG;;;;;;sCAGhH,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsC,UAAU,SAAS;;;;;;8CACxE,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsC,UAAU,aAAa;;;;;;8CAC5E,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsC,UAAU,aAAa;;;;;;8CAC5E,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;sCAEzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAsC,UAAU,iBAAiB;wCAAC;;;;;;;8CACjF,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAK3C,6LAAC;oBAAI,WAAU;8BACZ,AAAC;wBAAC;wBAAQ;wBAAS;qBAAM,CAAW,GAAG,CAAC,CAAC,uBACxC,6LAAC;4BAEC,SAAS,IAAM,kBAAkB;4BACjC,WAAW,CAAC,wDAAwD,EAClE,mBAAmB,SACf,8DACA,+CACJ;sCAED,WAAW,SAAS,gBAAgB,WAAW,UAAU,iBAAiB;2BARtE;;;;;;;;;;gBAcV,mBAAmB,UAAU,YAAY,MAAM,GAAG,mBACjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,MAAM;gCACtB,MAAM,aAAa,KAAK,KAAK,GAAG,IAAI,AAAC,KAAK,SAAS,GAAG,KAAK,KAAK,GAAI,MAAM;gCAC1E,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,QAAQ,GAAG,WAAW,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAGtC,6LAAC;4CAAI,WAAU;;gDAAwB;gDAAM,KAAK,IAAI;;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;gDAA2B,KAAK,KAAK,CAAC;gDAAY;;;;;;;;mCARzD;;;;;4BAWd;;;;;;;;;;;;gBAML,UAAU,gBAAgB,CAAC,MAAM,GAAG,mBACnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACZ,UAAU,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,6LAAC;oCAEC,WAAU;8CAET;mCAHI;;;;;;;;;;;;;;;;8BAWf,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;wBACrD,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAE;;;;;;;;;;;iDAGL,6LAAC;4BAAI,WAAU;sCACZ,gBACE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IACpE,GAAG,CAAC,CAAC,OAAO;gCACX,MAAM,UAAU,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI;gCACrC,MAAM,aAAa,AAAC,MAAM,cAAc,GAAG,MAAM,UAAU,GAAI;gCAE/D,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA0B,MAAM,QAAQ;;;;;;sEACvD,6LAAC;4DAAI,WAAU;sEACZ,YAAY,IAAI,UAAU,YAAY,IAAI,cAAc,GAAG,QAAQ,SAAS,CAAC;;;;;;;;;;;;8DAGlF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAqC,KAAK,KAAK,CAAC;gEAAY;;;;;;;sEAC3E,6LAAC;4DAAI,WAAU;;gEAAyB,MAAM,cAAc;gEAAC;gEAAE,MAAM,UAAU;;;;;;;;;;;;;;;;;;;sDAInF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,WAAW,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAIrC,6LAAC;4CAAI,WAAU;sDACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC5B,6LAAC;oDAEC,WAAW,CAAC,0BAA0B,EACpC,QAAQ,SAAS,GACb,qCACA,6BACJ;8DAED,QAAQ,IAAI;mDAPR;;;;;;;;;;;mCA1BN;;;;;4BAuCX;;;;;;;;;;;;;;;;;;;;;;;AAOhB;GArMwB;KAAA", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'info';\n  duration?: number;\n  onClose: () => void;\n}\n\nexport default function Toast({ message, type, duration = 3000, onClose }: ToastProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Show toast\n    setIsVisible(true);\n    \n    // Auto hide after duration\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(onClose, 300); // Wait for fade out animation\n    }, duration);\n\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n\n  const getToastStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-500/30 text-green-400';\n      case 'error':\n        return 'bg-gradient-to-r from-red-500/20 to-rose-500/20 border-red-500/30 text-red-400';\n      case 'info':\n      default:\n        return 'bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border-yellow-400/30 text-yellow-400';\n    }\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return '✅';\n      case 'error':\n        return '❌';\n      case 'info':\n      default:\n        return 'ℹ️';\n    }\n  };\n\n  return (\n    <div\n      className={`fixed top-4 right-4 z-50 p-4 rounded-lg border backdrop-blur-sm transition-all duration-300 ${\n        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'\n      } ${getToastStyles()}`}\n    >\n      <div className=\"flex items-center gap-3\">\n        <span className=\"text-lg\">{getIcon()}</span>\n        <span className=\"font-medium\">{message}</span>\n        <button\n          onClick={() => {\n            setIsVisible(false);\n            setTimeout(onClose, 300);\n          }}\n          className=\"ml-2 text-gray-400 hover:text-white transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n    </div>\n  );\n}\n\n// Toast context for global toast management\nimport { createContext, useContext, ReactNode } from 'react';\n\ninterface ToastContextType {\n  showToast: (message: string, type: 'success' | 'error' | 'info') => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport function ToastProvider({ children }: { children: ReactNode }) {\n  const [toasts, setToasts] = useState<Array<{ id: number; message: string; type: 'success' | 'error' | 'info' }>>([]);\n\n  const showToast = (message: string, type: 'success' | 'error' | 'info') => {\n    const id = Date.now();\n    setToasts(prev => [...prev, { id, message, type }]);\n  };\n\n  const removeToast = (id: number) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  return (\n    <ToastContext.Provider value={{ showToast }}>\n      {children}\n      {toasts.map(toast => (\n        <Toast\n          key={toast.id}\n          message={toast.message}\n          type={toast.type}\n          onClose={() => removeToast(toast.id)}\n        />\n      ))}\n    </ToastContext.Provider>\n  );\n}\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (context === undefined) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAWe,SAAS,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,IAAI,EAAE,OAAO,EAAc;;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,aAAa;YACb,aAAa;YAEb,2BAA2B;YAC3B,MAAM,QAAQ;yCAAW;oBACvB,aAAa;oBACb,WAAW,SAAS,MAAM,8BAA8B;gBAC1D;wCAAG;YAEH;mCAAO,IAAM,aAAa;;QAC5B;0BAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,4FAA4F,EACtG,YAAY,8BAA8B,2BAC3C,CAAC,EAAE,kBAAkB;kBAEtB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAU;8BAAW;;;;;;8BAC3B,6LAAC;oBAAK,WAAU;8BAAe;;;;;;8BAC/B,6LAAC;oBACC,SAAS;wBACP,aAAa;wBACb,WAAW,SAAS;oBACtB;oBACA,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GA7DwB;KAAA;;AAsExB,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAA2B;;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8E,EAAE;IAEnH,MAAM,YAAY,CAAC,SAAiB;QAClC,MAAM,KAAK,KAAK,GAAG;QACnB,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE;oBAAI;oBAAS;gBAAK;aAAE;IACpD;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAU;;YACvC;YACA,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;oBAEC,SAAS,MAAM,OAAO;oBACtB,MAAM,MAAM,IAAI;oBAChB,SAAS,IAAM,YAAY,MAAM,EAAE;mBAH9B,MAAM,EAAE;;;;;;;;;;;AAQvB;IAzBgB;MAAA;AA2BT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/src/app/tracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  RoutineItem, \n  DailyProgress, \n  saveDailyProgress, \n  getDailyProgress, \n  getUserStats,\n  checkForMidnightReset \n} from '@/utils/storage';\nimport { formatDate, formatTimeUntilMidnight } from '@/utils/dateUtils';\nimport HistoryView from '@/components/HistoryView';\nimport { ToastProvider, useToast } from '@/components/Toast';\n\nconst DEFAULT_ROUTINES = [\n  { id: 'prayer', name: '🙏 Prayer', completed: false },\n  { id: 'study', name: '📚 Study', completed: false },\n  { id: 'hygiene', name: '🚿 Hygiene', completed: false },\n  { id: 'work', name: '💼 Work', completed: false },\n  { id: 'exercise', name: '💪 Exercise', completed: false },\n  { id: 'reading', name: '📖 Reading', completed: false },\n  { id: 'meditation', name: '🧘 Meditation', completed: false },\n  { id: 'family', name: '👨‍👩‍👧‍👦 Family Time', completed: false },\n];\n\nfunction RoutineTrackerContent() {\n  const [userName, setUserName] = useState('');\n  const [routines, setRoutines] = useState<RoutineItem[]>(DEFAULT_ROUTINES);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [currentDate, setCurrentDate] = useState('');\n  const [progress, setProgress] = useState(0);\n  const [timeUntilReset, setTimeUntilReset] = useState('');\n  const [userStats, setUserStats] = useState({ currentStreak: 0, totalDays: 0 });\n  const [showHistory, setShowHistory] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { showToast } = useToast();\n\n  useEffect(() => {\n    const today = formatDate(new Date());\n    setCurrentDate(today);\n    \n    // Check for midnight reset\n    if (checkForMidnightReset()) {\n      // New day detected, reset the interface\n      setIsSubmitted(false);\n      setUserName('');\n      setRoutines(DEFAULT_ROUTINES);\n      setProgress(0);\n    }\n    \n    // Load today's data from localStorage\n    const savedData = getDailyProgress(today);\n    if (savedData) {\n      setUserName(savedData.userName);\n      setRoutines(savedData.routines);\n      setIsSubmitted(true);\n      setProgress((savedData.completedCount / savedData.totalCount) * 100);\n    }\n    \n    // Load user stats\n    const stats = getUserStats();\n    setUserStats(stats);\n    \n    // Update time until reset every minute\n    const updateTimer = () => {\n      setTimeUntilReset(formatTimeUntilMidnight());\n    };\n    \n    updateTimer();\n    const interval = setInterval(updateTimer, 60000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  const handleRoutineToggle = (id: string) => {\n    setRoutines(prev => \n      prev.map(routine => \n        routine.id === id \n          ? { ...routine, completed: !routine.completed }\n          : routine\n      )\n    );\n  };\n\n  const handleSubmit = () => {\n    if (!userName.trim()) {\n      showToast('Please enter your name first! ✨', 'error');\n      return;\n    }\n\n    const completedCount = routines.filter(r => r.completed).length;\n    const totalCount = routines.length;\n    const progressPercentage = (completedCount / totalCount) * 100;\n\n    const dailyProgress: DailyProgress = {\n      date: currentDate,\n      userName: userName.trim(),\n      routines,\n      completedCount,\n      totalCount,\n      submittedAt: new Date().toISOString(),\n      lastUpdated: new Date().toISOString()\n    };\n\n    // Save using enhanced storage\n    saveDailyProgress(dailyProgress);\n    \n    // Update local state\n    setIsSubmitted(true);\n    setProgress(progressPercentage);\n\n    // Refresh user stats\n    const stats = getUserStats();\n    setUserStats(stats);\n\n    // Show success message\n    const completionMessage = completedCount === totalCount\n      ? `🎉 Perfect day! All ${totalCount} routines completed!`\n      : `✅ Progress saved! ${completedCount}/${totalCount} routines completed.`;\n    showToast(completionMessage, 'success');\n  };\n\n  const handleUpdate = () => {\n    setIsSubmitted(false);\n    showToast('You can now update your progress! ✏️', 'info');\n  };\n\n  const completedCount = routines.filter(r => r.completed).length;\n  const totalCount = routines.length;\n\n  return (\n    <div className=\"min-h-screen p-4 sm:p-8\">\n      {/* Background Effects */}\n      <div className=\"fixed inset-0 pointer-events-none\">\n        <div className=\"absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-full blur-3xl floating\"></div>\n        <div className=\"absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-orange-500/10 to-yellow-600/10 rounded-full blur-3xl floating\" style={{animationDelay: '2s'}}></div>\n      </div>\n\n      <div className=\"max-w-2xl mx-auto relative z-10\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-5xl sm:text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-600 bg-clip-text text-transparent\">\n            Digital Routine\n          </h1>\n          <h2 className=\"text-3xl sm:text-4xl font-bold mb-6 text-white\">\n            & Results Tracker\n          </h2>\n          <p className=\"text-lg text-gray-300 max-w-lg mx-auto leading-relaxed\">\n            Track your daily growth with intention. No pressure, just accountability and reflection. ✨\n          </p>\n          \n          {/* User Stats */}\n          {userStats.totalDays > 0 && (\n            <div className=\"flex justify-center gap-6 mt-6\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-yellow-400\">{userStats.currentStreak}</div>\n                <div className=\"text-sm text-gray-400\">Current Streak</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-yellow-400\">{userStats.totalDays}</div>\n                <div className=\"text-sm text-gray-400\">Total Days</div>\n              </div>\n              <button\n                onClick={() => setShowHistory(true)}\n                className=\"text-center hover:scale-105 transition-transform cursor-pointer\"\n              >\n                <div className=\"text-2xl font-bold text-yellow-400\">📊</div>\n                <div className=\"text-sm text-gray-400\">View History</div>\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Main Card */}\n        <div className=\"card glass-effect golden-glow mb-8\">\n          {/* Date Display */}\n          <div className=\"text-center mb-8\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full border border-yellow-400/30\">\n              <span className=\"text-yellow-400 font-mono text-sm\">📅 {currentDate}</span>\n            </div>\n            {timeUntilReset && (\n              <div className=\"mt-2 text-xs text-gray-500\">\n                ⏰ {timeUntilReset}\n              </div>\n            )}\n          </div>\n\n          {/* Name Input */}\n          {!isSubmitted && (\n            <div className=\"mb-8\">\n              <label className=\"block text-lg font-semibold text-white mb-3\">\n                What's your name? ✨\n              </label>\n              <input\n                type=\"text\"\n                value={userName}\n                onChange={(e) => setUserName(e.target.value)}\n                placeholder=\"Enter your name here...\"\n                className=\"input-field text-lg\"\n                autoFocus\n              />\n            </div>\n          )}\n\n          {/* Welcome Message */}\n          {isSubmitted && userName && (\n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl font-bold text-yellow-400 mb-2\">\n                Welcome back, {userName}! 🌟\n              </h3>\n              <p className=\"text-gray-300\">\n                {completedCount === totalCount \n                  ? \"Amazing! You've completed all your routines today! 🎉\"\n                  : `You've completed ${completedCount} out of ${totalCount} routines today.`\n                }\n              </p>\n            </div>\n          )}\n\n          {/* Progress Bar */}\n          {isSubmitted && (\n            <div className=\"mb-8\">\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-300\">Daily Progress</span>\n                <span className=\"text-sm font-bold text-yellow-400\">{Math.round(progress)}%</span>\n              </div>\n              <div className=\"progress-bar\">\n                <div \n                  className=\"progress-fill\" \n                  style={{ width: `${progress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Routines List */}\n          <div className=\"space-y-3 mb-8\">\n            <h3 className=\"text-xl font-semibold text-white mb-4\">\n              {isSubmitted ? \"Today's Achievements:\" : \"Today's Goals:\"}\n            </h3>\n            \n            {routines.map((routine) => (\n              <label\n                key={routine.id}\n                className={`checkbox-container ${routine.completed ? 'checked' : ''} ${isSubmitted ? 'pointer-events-none opacity-75' : ''}`}\n              >\n                <input\n                  type=\"checkbox\"\n                  checked={routine.completed}\n                  onChange={() => handleRoutineToggle(routine.id)}\n                  disabled={isSubmitted}\n                />\n                <div className=\"checkmark\"></div>\n                <span className=\"checkbox-label\">{routine.name}</span>\n              </label>\n            ))}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-4 justify-center\">\n            {!isSubmitted ? (\n              <button\n                onClick={handleSubmit}\n                className=\"btn-primary px-8 py-3 text-lg font-bold\"\n                disabled={!userName.trim()}\n              >\n                Submit Progress 🚀\n              </button>\n            ) : (\n              <>\n                <button\n                  onClick={handleUpdate}\n                  className=\"btn-secondary px-8 py-3 text-lg\"\n                >\n                  Update Progress ✏️\n                </button>\n                {userStats.totalDays > 0 && (\n                  <button\n                    onClick={() => setShowHistory(true)}\n                    className=\"btn-secondary px-6 py-3 text-lg\"\n                  >\n                    📊 History\n                  </button>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center text-gray-400 text-sm\">\n          <p>Your system. No judgment. Just growth. 📊✨</p>\n          <p className=\"mt-2\">Resets at midnight • History preserved forever</p>\n        </div>\n      </div>\n\n      {/* History Modal */}\n      {showHistory && (\n        <HistoryView onClose={() => setShowHistory(false)} />\n      )}\n    </div>\n  );\n}\n\nexport default function RoutineTracker() {\n  return (\n    <ToastProvider>\n      <RoutineTrackerContent />\n    </ToastProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;;;AAbA;;;;;;AAeA,MAAM,mBAAmB;IACvB;QAAE,IAAI;QAAU,MAAM;QAAa,WAAW;IAAM;IACpD;QAAE,IAAI;QAAS,MAAM;QAAY,WAAW;IAAM;IAClD;QAAE,IAAI;QAAW,MAAM;QAAc,WAAW;IAAM;IACtD;QAAE,IAAI;QAAQ,MAAM;QAAW,WAAW;IAAM;IAChD;QAAE,IAAI;QAAY,MAAM;QAAe,WAAW;IAAM;IACxD;QAAE,IAAI;QAAW,MAAM;QAAc,WAAW;IAAM;IACtD;QAAE,IAAI;QAAc,MAAM;QAAiB,WAAW;IAAM;IAC5D;QAAE,IAAI;QAAU,MAAM;QAA2B,WAAW;IAAM;CACnE;AAED,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,eAAe;QAAG,WAAW;IAAE;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,WAAQ,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,QAAQ,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,IAAI;YAC7B,eAAe;YAEf,2BAA2B;YAC3B,IAAI,CAAA,GAAA,0HAAA,CAAA,wBAAqB,AAAD,KAAK;gBAC3B,wCAAwC;gBACxC,eAAe;gBACf,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YAEA,sCAAsC;YACtC,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE;YACnC,IAAI,WAAW;gBACb,YAAY,UAAU,QAAQ;gBAC9B,YAAY,UAAU,QAAQ;gBAC9B,eAAe;gBACf,YAAY,AAAC,UAAU,cAAc,GAAG,UAAU,UAAU,GAAI;YAClE;YAEA,kBAAkB;YAClB,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;YACzB,aAAa;YAEb,uCAAuC;YACvC,MAAM;+DAAc;oBAClB,kBAAkB,CAAA,GAAA,4HAAA,CAAA,0BAAuB,AAAD;gBAC1C;;YAEA;YACA,MAAM,WAAW,YAAY,aAAa;YAE1C;mDAAO,IAAM,cAAc;;QAC7B;0CAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,UACP,QAAQ,EAAE,KAAK,KACX;oBAAE,GAAG,OAAO;oBAAE,WAAW,CAAC,QAAQ,SAAS;gBAAC,IAC5C;IAGV;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,UAAU,mCAAmC;YAC7C;QACF;QAEA,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAC/D,MAAM,aAAa,SAAS,MAAM;QAClC,MAAM,qBAAqB,AAAC,iBAAiB,aAAc;QAE3D,MAAM,gBAA+B;YACnC,MAAM;YACN,UAAU,SAAS,IAAI;YACvB;YACA;YACA;YACA,aAAa,IAAI,OAAO,WAAW;YACnC,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,8BAA8B;QAC9B,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE;QAElB,qBAAqB;QACrB,eAAe;QACf,YAAY;QAEZ,qBAAqB;QACrB,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;QACzB,aAAa;QAEb,uBAAuB;QACvB,MAAM,oBAAoB,mBAAmB,aACzC,CAAC,oBAAoB,EAAE,WAAW,oBAAoB,CAAC,GACvD,CAAC,kBAAkB,EAAE,eAAe,CAAC,EAAE,WAAW,oBAAoB,CAAC;QAC3E,UAAU,mBAAmB;IAC/B;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,UAAU,wCAAwC;IACpD;IAEA,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;IAC/D,MAAM,aAAa,SAAS,MAAM;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAA4H,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;;;;;;;0BAGzK,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkI;;;;;;0CAGhJ,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,6LAAC;gCAAE,WAAU;0CAAyD;;;;;;4BAKrE,UAAU,SAAS,GAAG,mBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsC,UAAU,aAAa;;;;;;0DAC5E,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsC,UAAU,SAAS;;;;;;0DACxE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAAoC;gDAAI;;;;;;;;;;;;oCAEzD,gCACC,6LAAC;wCAAI,WAAU;;4CAA6B;4CACvC;;;;;;;;;;;;;4BAMR,CAAC,6BACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA8C;;;;;;kDAG/D,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,aAAY;wCACZ,WAAU;wCACV,SAAS;;;;;;;;;;;;4BAMd,eAAe,0BACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAA0C;4CACvC;4CAAS;;;;;;;kDAE1B,6LAAC;wCAAE,WAAU;kDACV,mBAAmB,aAChB,0DACA,CAAC,iBAAiB,EAAE,eAAe,QAAQ,EAAE,WAAW,gBAAgB,CAAC;;;;;;;;;;;;4BAOlF,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,6LAAC;gDAAK,WAAU;;oDAAqC,KAAK,KAAK,CAAC;oDAAU;;;;;;;;;;;;;kDAE5E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;0CAOvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,cAAc,0BAA0B;;;;;;oCAG1C,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAEC,WAAW,CAAC,mBAAmB,EAAE,QAAQ,SAAS,GAAG,YAAY,GAAG,CAAC,EAAE,cAAc,mCAAmC,IAAI;;8DAE5H,6LAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,SAAS;oDAC1B,UAAU,IAAM,oBAAoB,QAAQ,EAAE;oDAC9C,UAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAkB,QAAQ,IAAI;;;;;;;2CAVzC,QAAQ,EAAE;;;;;;;;;;;0CAgBrB,6LAAC;gCAAI,WAAU;0CACZ,CAAC,4BACA,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU,CAAC,SAAS,IAAI;8CACzB;;;;;yDAID;;sDACE,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;wCAGA,UAAU,SAAS,GAAG,mBACrB,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;kCAUX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAO;;;;;;;;;;;;;;;;;;YAKvB,6BACC,6LAAC,oIAAA,CAAA,UAAW;gBAAC,SAAS,IAAM,eAAe;;;;;;;;;;;;AAInD;GArRS;;QAUe,8HAAA,CAAA,WAAQ;;;KAVvB;AAuRM,SAAS;IACtB,qBACE,6LAAC,8HAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/RR%20tracker/routine-tracker/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}