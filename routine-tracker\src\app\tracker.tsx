'use client';

import { useState, useEffect } from 'react';
import { 
  RoutineItem, 
  DailyProgress, 
  saveDailyProgress, 
  getDailyProgress, 
  getUserStats,
  checkForMidnightReset 
} from '@/utils/storage';
import { formatDate, formatTimeUntilMidnight } from '@/utils/dateUtils';
import HistoryView from '@/components/HistoryView';
import { ToastProvider, useToast } from '@/components/Toast';
import SignIn from '@/components/SignIn';
import Dashboard from '@/components/Dashboard';

const DEFAULT_ROUTINES = [
  { id: 'prayer', name: '🙏 Prayer', completed: false },
  { id: 'study', name: '📚 Study', completed: false },
  { id: 'hygiene', name: '🚿 Hygiene', completed: false },
  { id: 'work', name: '💼 Work', completed: false },
  { id: 'exercise', name: '💪 Exercise', completed: false },
  { id: 'reading', name: '📖 Reading', completed: false },
  { id: 'meditation', name: '🧘 Meditation', completed: false },
  { id: 'family', name: '👨‍👩‍👧‍👦 Family Time', completed: false },
];

function RoutineTrackerContent() {
  const [userName, setUserName] = useState('');
  const [isSignedIn, setIsSignedIn] = useState(false);
  const [routines, setRoutines] = useState<RoutineItem[]>(DEFAULT_ROUTINES);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [currentDate, setCurrentDate] = useState('');
  const [progress, setProgress] = useState(0);
  const [timeUntilReset, setTimeUntilReset] = useState('');
  const [userStats, setUserStats] = useState({ currentStreak: 0, totalDays: 0 });
  const [showHistory, setShowHistory] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    const today = formatDate(new Date());
    setCurrentDate(today);

    // Check for saved user
    const savedUser = localStorage.getItem('routine-user-name');
    if (savedUser) {
      setUserName(savedUser);
      setIsSignedIn(true);
    }

    // Check for midnight reset
    if (checkForMidnightReset()) {
      // New day detected, reset the interface
      setIsSubmitted(false);
      setRoutines(DEFAULT_ROUTINES);
      setProgress(0);
    }

    // Load today's data from localStorage
    const savedData = getDailyProgress(today);
    if (savedData && savedUser) {
      setRoutines(savedData.routines);
      setIsSubmitted(true);
      setProgress((savedData.completedCount / savedData.totalCount) * 100);
    }

    // Load user stats
    const stats = getUserStats();
    setUserStats(stats);

    // Update time until reset every minute
    const updateTimer = () => {
      setTimeUntilReset(formatTimeUntilMidnight());
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000);

    return () => clearInterval(interval);
  }, []);

  const handleRoutineToggle = (id: string) => {
    setRoutines(prev => 
      prev.map(routine => 
        routine.id === id 
          ? { ...routine, completed: !routine.completed }
          : routine
      )
    );
  };

  const handleSubmit = () => {
    if (!userName.trim()) {
      showToast('Please enter your name first! ✨', 'error');
      return;
    }

    const completedCount = routines.filter(r => r.completed).length;
    const totalCount = routines.length;
    const progressPercentage = (completedCount / totalCount) * 100;

    const dailyProgress: DailyProgress = {
      date: currentDate,
      userName: userName.trim(),
      routines,
      completedCount,
      totalCount,
      submittedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    // Save using enhanced storage
    saveDailyProgress(dailyProgress);
    
    // Update local state
    setIsSubmitted(true);
    setProgress(progressPercentage);

    // Refresh user stats
    const stats = getUserStats();
    setUserStats(stats);

    // Show success message
    const completionMessage = completedCount === totalCount
      ? `🎉 Perfect day! All ${totalCount} routines completed!`
      : `✅ Progress saved! ${completedCount}/${totalCount} routines completed.`;
    showToast(completionMessage, 'success');
  };

  const handleUpdate = () => {
    setIsSubmitted(false);
    showToast('You can now update your progress! ✏️', 'info');
  };

  const handleSignIn = (name: string) => {
    setUserName(name);
    setIsSignedIn(true);
    localStorage.setItem('routine-user-name', name);
    showToast(`Welcome, ${name}! Let's start tracking your routines! 🌟`, 'success');
  };

  const handleSignOut = () => {
    setIsSignedIn(false);
    setUserName('');
    setIsSubmitted(false);
    setRoutines(DEFAULT_ROUTINES);
    setProgress(0);
    setShowHistory(false);
    localStorage.removeItem('routine-user-name');
    showToast('Signed out successfully! See you soon! 👋', 'info');
  };

  // Show sign-in screen if not signed in
  if (!isSignedIn) {
    return <SignIn onSignIn={handleSignIn} />;
  }

  // Show dashboard if signed in
  return (
    <>
      <Dashboard
        userName={userName}
        routines={routines}
        onRoutineToggle={handleRoutineToggle}
        onSubmit={handleSubmit}
        onUpdate={handleUpdate}
        onShowHistory={() => setShowHistory(true)}
        onSignOut={handleSignOut}
        isSubmitted={isSubmitted}
        progress={progress}
        currentDate={currentDate}
        timeUntilReset={timeUntilReset}
        userStats={userStats}
      />

      {/* History Modal */}
      {showHistory && (
        <HistoryView onClose={() => setShowHistory(false)} />
      )}
    </>
  );
}

export default function RoutineTracker() {
  return (
    <ToastProvider>
      <RoutineTrackerContent />
    </ToastProvider>
  );
}
