'use client';

import { useState, useEffect } from 'react';
import { 
  RoutineItem, 
  DailyProgress, 
  saveDailyProgress, 
  getDailyProgress, 
  getUserStats,
  checkForMidnightReset 
} from '@/utils/storage';
import { formatDate, formatTimeUntilMidnight } from '@/utils/dateUtils';
import HistoryView from '@/components/HistoryView';
import { ToastProvider, useToast } from '@/components/Toast';

const DEFAULT_ROUTINES = [
  { id: 'prayer', name: '🙏 Prayer', completed: false },
  { id: 'study', name: '📚 Study', completed: false },
  { id: 'hygiene', name: '🚿 Hygiene', completed: false },
  { id: 'work', name: '💼 Work', completed: false },
  { id: 'exercise', name: '💪 Exercise', completed: false },
  { id: 'reading', name: '📖 Reading', completed: false },
  { id: 'meditation', name: '🧘 Meditation', completed: false },
  { id: 'family', name: '👨‍👩‍👧‍👦 Family Time', completed: false },
];

function RoutineTrackerContent() {
  const [userName, setUserName] = useState('');
  const [routines, setRoutines] = useState<RoutineItem[]>(DEFAULT_ROUTINES);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [currentDate, setCurrentDate] = useState('');
  const [progress, setProgress] = useState(0);
  const [timeUntilReset, setTimeUntilReset] = useState('');
  const [userStats, setUserStats] = useState({ currentStreak: 0, totalDays: 0 });
  const [showHistory, setShowHistory] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    const today = formatDate(new Date());
    setCurrentDate(today);
    
    // Check for midnight reset
    if (checkForMidnightReset()) {
      // New day detected, reset the interface
      setIsSubmitted(false);
      setUserName('');
      setRoutines(DEFAULT_ROUTINES);
      setProgress(0);
    }
    
    // Load today's data from localStorage
    const savedData = getDailyProgress(today);
    if (savedData) {
      setUserName(savedData.userName);
      setRoutines(savedData.routines);
      setIsSubmitted(true);
      setProgress((savedData.completedCount / savedData.totalCount) * 100);
    }
    
    // Load user stats
    const stats = getUserStats();
    setUserStats(stats);
    
    // Update time until reset every minute
    const updateTimer = () => {
      setTimeUntilReset(formatTimeUntilMidnight());
    };
    
    updateTimer();
    const interval = setInterval(updateTimer, 60000);
    
    return () => clearInterval(interval);
  }, []);

  const handleRoutineToggle = (id: string) => {
    setRoutines(prev => 
      prev.map(routine => 
        routine.id === id 
          ? { ...routine, completed: !routine.completed }
          : routine
      )
    );
  };

  const handleSubmit = () => {
    if (!userName.trim()) {
      showToast('Please enter your name first! ✨', 'error');
      return;
    }

    const completedCount = routines.filter(r => r.completed).length;
    const totalCount = routines.length;
    const progressPercentage = (completedCount / totalCount) * 100;

    const dailyProgress: DailyProgress = {
      date: currentDate,
      userName: userName.trim(),
      routines,
      completedCount,
      totalCount,
      submittedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    // Save using enhanced storage
    saveDailyProgress(dailyProgress);
    
    // Update local state
    setIsSubmitted(true);
    setProgress(progressPercentage);

    // Refresh user stats
    const stats = getUserStats();
    setUserStats(stats);

    // Show success message
    const completionMessage = completedCount === totalCount
      ? `🎉 Perfect day! All ${totalCount} routines completed!`
      : `✅ Progress saved! ${completedCount}/${totalCount} routines completed.`;
    showToast(completionMessage, 'success');
  };

  const handleUpdate = () => {
    setIsSubmitted(false);
    showToast('You can now update your progress! ✏️', 'info');
  };

  const completedCount = routines.filter(r => r.completed).length;
  const totalCount = routines.length;

  return (
    <div className="min-h-screen p-4 sm:p-8">
      {/* Background Effects */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-orange-500/10 to-yellow-600/10 rounded-full blur-3xl floating" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="max-w-2xl mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl sm:text-6xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-600 bg-clip-text text-transparent">
            Digital Routine
          </h1>
          <h2 className="text-3xl sm:text-4xl font-bold mb-6 text-white">
            & Results Tracker
          </h2>
          <p className="text-lg text-gray-300 max-w-lg mx-auto leading-relaxed">
            Track your daily growth with intention. No pressure, just accountability and reflection. ✨
          </p>
          
          {/* User Stats */}
          {userStats.totalDays > 0 && (
            <div className="flex justify-center gap-6 mt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{userStats.currentStreak}</div>
                <div className="text-sm text-gray-400">Current Streak</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">{userStats.totalDays}</div>
                <div className="text-sm text-gray-400">Total Days</div>
              </div>
              <button
                onClick={() => setShowHistory(true)}
                className="text-center hover:scale-105 transition-transform cursor-pointer"
              >
                <div className="text-2xl font-bold text-yellow-400">📊</div>
                <div className="text-sm text-gray-400">View History</div>
              </button>
            </div>
          )}
        </div>

        {/* Main Card */}
        <div className="card glass-effect golden-glow mb-8">
          {/* Date Display */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-yellow-400/20 to-orange-500/20 rounded-full border border-yellow-400/30">
              <span className="text-yellow-400 font-mono text-sm">📅 {currentDate}</span>
            </div>
            {timeUntilReset && (
              <div className="mt-2 text-xs text-gray-500">
                ⏰ {timeUntilReset}
              </div>
            )}
          </div>

          {/* Name Input */}
          {!isSubmitted && (
            <div className="mb-8">
              <label className="block text-lg font-semibold text-white mb-3">
                What's your name? ✨
              </label>
              <input
                type="text"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                placeholder="Enter your name here..."
                className="input-field text-lg"
                autoFocus
              />
            </div>
          )}

          {/* Welcome Message */}
          {isSubmitted && userName && (
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-yellow-400 mb-2">
                Welcome back, {userName}! 🌟
              </h3>
              <p className="text-gray-300">
                {completedCount === totalCount 
                  ? "Amazing! You've completed all your routines today! 🎉"
                  : `You've completed ${completedCount} out of ${totalCount} routines today.`
                }
              </p>
            </div>
          )}

          {/* Progress Bar */}
          {isSubmitted && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-300">Daily Progress</span>
                <span className="text-sm font-bold text-yellow-400">{Math.round(progress)}%</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Routines List */}
          <div className="space-y-3 mb-8">
            <h3 className="text-xl font-semibold text-white mb-4">
              {isSubmitted ? "Today's Achievements:" : "Today's Goals:"}
            </h3>
            
            {routines.map((routine) => (
              <label
                key={routine.id}
                className={`checkbox-container ${routine.completed ? 'checked' : ''} ${isSubmitted ? 'pointer-events-none opacity-75' : ''}`}
              >
                <input
                  type="checkbox"
                  checked={routine.completed}
                  onChange={() => handleRoutineToggle(routine.id)}
                  disabled={isSubmitted}
                />
                <div className="checkmark"></div>
                <span className="checkbox-label">{routine.name}</span>
              </label>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            {!isSubmitted ? (
              <button
                onClick={handleSubmit}
                className="btn-primary px-8 py-3 text-lg font-bold"
                disabled={!userName.trim()}
              >
                Submit Progress 🚀
              </button>
            ) : (
              <>
                <button
                  onClick={handleUpdate}
                  className="btn-secondary px-8 py-3 text-lg"
                >
                  Update Progress ✏️
                </button>
                {userStats.totalDays > 0 && (
                  <button
                    onClick={() => setShowHistory(true)}
                    className="btn-secondary px-6 py-3 text-lg"
                  >
                    📊 History
                  </button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-gray-400 text-sm">
          <p>Your system. No judgment. Just growth. 📊✨</p>
          <p className="mt-2">Resets at midnight • History preserved forever</p>
        </div>
      </div>

      {/* History Modal */}
      {showHistory && (
        <HistoryView onClose={() => setShowHistory(false)} />
      )}
    </div>
  );
}

export default function RoutineTracker() {
  return (
    <ToastProvider>
      <RoutineTrackerContent />
    </ToastProvider>
  );
}
